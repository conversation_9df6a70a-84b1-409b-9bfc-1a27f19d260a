'use client';

import { useState } from 'react';
import Image from 'next/image';
import Button from './Button';

interface Feature {
  text: string;
  details?: string;
  highlight?: string;
  example?: string;
}

interface TimeFeature {
  text: string;
  duration: string;
}

interface Ped {
  name: string;
  image: string;
}

interface ClothingItem {
  name: string;
  description: string;
  variants: number;
  image?: string; // Optional image path for clothing item
  placeholder?: string; // Placeholder text for clothing name
}

interface VIPCardProps {
  tier: number;
  price: number;
  title?: string;
  description: string;
  icon: string;
  mainFeatures: Feature[];
  timeFeatures: TimeFeature[];
  clothing: ClothingItem[];
  peds: Ped[];
  isPopular?: boolean;
  tiltDirection?: 'left' | 'right';
  pattern?: string;
  className?: string;
  currency?: string;
}

const VIPCard = ({
  tier,
  price,
  title,
  description,
  icon,
  mainFeatures,
  timeFeatures,
  clothing,
  peds,
  isPopular = false,
  className = "",
  currency = "₹"
}: VIPCardProps) => {
  const [activeTab, setActiveTab] = useState<'features' | 'peds' | 'clothing'>('features');
  const [isHovered, setIsHovered] = useState(false);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Function to get rank-specific colors for clothing items
  const getClothingRankColors = (itemIndex: number) => {
    const itemTier = itemIndex + 1; // Convert index to tier number
    switch (itemTier) {
      case 1:
        return {
          bg: 'from-red-800/30 to-red-700/20',
          border: 'border-red-700/50',
          hoverBorder: 'hover:border-red-600/80',
          shadow: 'hover:shadow-red-700/30',
          badge: 'bg-red-700/20 border-red-600/40 text-red-300',
          particles: 'bg-red-600'
        };
      case 2:
        return {
          bg: 'from-gray-400/30 to-slate-500/20',
          border: 'border-gray-400/50',
          hoverBorder: 'hover:border-gray-300/80',
          shadow: 'hover:shadow-gray-400/30',
          badge: 'bg-gray-400/20 border-gray-300/40 text-gray-200',
          particles: 'bg-gray-300'
        };
      case 3:
        return {
          bg: 'from-amber-600/30 to-amber-500/20',
          border: 'border-amber-500/50',
          hoverBorder: 'hover:border-amber-400/80',
          shadow: 'hover:shadow-amber-500/30',
          badge: 'bg-amber-500/20 border-amber-400/40 text-amber-300',
          particles: 'bg-amber-400'
        };
      case 4:
        return {
          bg: 'from-violet-500/30 to-violet-400/20',
          border: 'border-violet-400/50',
          hoverBorder: 'hover:border-violet-300/80',
          shadow: 'hover:shadow-violet-400/30',
          badge: 'bg-violet-400/20 border-violet-300/40 text-violet-200',
          particles: 'bg-violet-300'
        };
      case 5:
        return {
          bg: 'from-yellow-400/30 to-amber-500/20',
          border: 'border-yellow-400/50',
          hoverBorder: 'hover:border-yellow-300/80',
          shadow: 'hover:shadow-yellow-400/30',
          badge: 'bg-yellow-400/20 border-yellow-300/40 text-yellow-200',
          particles: 'bg-yellow-300'
        };
      default:
        return {
          bg: 'from-gray-600/30 to-gray-700/20',
          border: 'border-gray-500/50',
          hoverBorder: 'hover:border-gray-400/80',
          shadow: 'hover:shadow-gray-500/30',
          badge: 'bg-gray-500/20 border-gray-400/40 text-gray-300',
          particles: 'bg-gray-400'
        };
    }
  };

  // Function to reorder clothing items - current tier first, then others
  const getOrderedClothing = () => {
    if (!clothing || clothing.length === 0) return [];

    // Find current tier item (index = tier - 1)
    const currentTierIndex = tier - 1;
    const currentTierItem = clothing[currentTierIndex];

    // Get all other items
    const otherItems = clothing.filter((_, index) => index !== currentTierIndex);

    // Return current tier first, then others
    return currentTierItem ? [currentTierItem, ...otherItems] : clothing;
  };

  // Get tier-specific styling and icons
  let tierBorder = "border-orange-500";
  let tierText = "text-orange-400";
  let tierAccent = "bg-orange-500";
  let tierGradient = "from-orange-500/30 to-yellow-500/30";
  let tierGlow = "shadow-orange-500/30";
  let tierIcon = "fas fa-star";
  let isSpecialTier = false;

  if (tier === 1) {
    // Maroon color
    tierBorder = "border-red-800";
    tierText = "text-red-300";
    tierAccent = "bg-red-800";
    tierGradient = "from-red-800/30 to-red-700/30";
    tierGlow = "shadow-red-800/30";
    tierIcon = "fas fa-medal";
  } else if (tier === 2) {
    // Silver color
    tierBorder = "border-gray-400";
    tierText = "text-gray-300";
    tierAccent = "bg-gray-400";
    tierGradient = "from-gray-400/30 to-gray-300/30";
    tierGlow = "shadow-gray-400/30";
    tierIcon = "fas fa-award";
  } else if (tier === 3) {
    // Bronze color
    tierBorder = "border-amber-600";
    tierText = "text-amber-400";
    tierAccent = "bg-amber-600";
    tierGradient = "from-amber-600/30 to-amber-500/30";
    tierGlow = "shadow-amber-600/30";
    tierIcon = "fas fa-trophy";
  } else if (tier === 4) {
    // Violet color
    tierBorder = "border-violet-500";
    tierText = "text-violet-400";
    tierAccent = "bg-violet-500";
    tierGradient = "from-violet-500/30 to-violet-400/30";
    tierGlow = "shadow-violet-500/30";
    tierIcon = "fas fa-gem";
    isSpecialTier = true;
  } else if (tier === 5) {
    // Golden color (brighter)
    tierBorder = "border-yellow-400";
    tierText = "text-yellow-300";
    tierAccent = "bg-yellow-400";
    tierGradient = "from-yellow-400/30 to-yellow-300/30";
    tierGlow = "shadow-yellow-400/30";
    tierIcon = "fas fa-crown";
    isSpecialTier = true;
  }

  return (
    <div className={`relative ${className} group`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-2 right-4 z-30">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 px-3 py-1 rounded-md text-black text-xs font-bold shadow-lg">
            POPULAR
          </div>
        </div>
      )}

      {/* Card Glow Effect - All Cards */}
      <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${tierGradient} opacity-20 blur-xl pointer-events-none`}></div>

      {/* Enhanced Glow for Special Tiers */}
      {isSpecialTier && (
        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${tierGradient} opacity-40 blur-2xl pointer-events-none animate-pulse`}></div>
      )}

      <div className={`bg-black/95 backdrop-blur-sm rounded-3xl border-2 ${tierBorder} hover:border-opacity-100 transition-all duration-500 overflow-hidden relative h-full flex flex-col shadow-xl ${tierGlow} group-hover:shadow-2xl group-hover:${tierGlow.replace('/30', '/50')}`}>

        {/* Header */}
        <div className="p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-900/50 to-black/50 relative overflow-hidden">
          {/* Shine Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"></div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${tierGradient} border-2 ${tierBorder} flex items-center justify-center ${isSpecialTier ? 'animate-pulse shadow-lg shadow-current/50' : 'shadow-lg'}`}>
                <i className={`${tierIcon} ${tierText} text-2xl`}></i>
              </div>
              <div>
                <h3 className="text-3xl font-bold text-white mb-1">{title || `VIP ${tier}`}</h3>
                <p className="text-gray-300 text-lg font-medium">{description}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-5xl font-bold text-white mb-1">{currency}{price.toLocaleString()}</div>
              <div className="text-gray-400 text-base font-medium">One-time purchase</div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="px-8 py-4 border-b border-gray-700/30 bg-gray-900/30">
          <div className="flex space-x-3">
            <button
              onClick={() => setActiveTab('features')}
              className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${
                activeTab === 'features'
                  ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'
              }`}
            >
              Features
            </button>
            {peds.length > 0 && (
              <button
                onClick={() => setActiveTab('peds')}
                className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${
                  activeTab === 'peds'
                    ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'
                }`}
              >
                Peds ({peds.length})
              </button>
            )}
            {clothing.length > 0 && (
              <button
                onClick={() => setActiveTab('clothing')}
                className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${
                  activeTab === 'clothing'
                    ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'
                }`}
              >
                Clothing
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-8 flex-grow">
          {/* Features Tab */}
          {activeTab === 'features' && (
            <div className="space-y-5">
              {mainFeatures.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4 p-5 rounded-2xl bg-gray-800/40 border border-gray-700/40 hover:bg-gray-800/60 hover:border-gray-600/60 transition-all duration-300 shadow-lg">
                  <div className={`w-3 h-3 rounded-full ${tierAccent} mt-3 flex-shrink-0 shadow-lg`}></div>
                  <div className="flex-grow">
                    <div className="text-white font-bold text-lg mb-3">{feature.text}</div>
                    {feature.details && (
                      <div className="text-gray-300 text-base mb-4">{feature.details}</div>
                    )}
                    <div className="flex flex-wrap gap-3">
                      {feature.highlight && (
                        <span className={`${tierText} text-base font-bold bg-gray-800/80 px-4 py-2 rounded-xl border-2 ${tierBorder} shadow-lg`}>
                          {feature.highlight}
                        </span>
                      )}
                      {feature.example && (
                        <span className="text-gray-200 text-base font-mono bg-gray-900/80 px-4 py-2 rounded-xl border border-gray-600 shadow-lg">
                          {feature.example}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Time Features */}
              {timeFeatures.length > 0 && (
                <div className="mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/40 shadow-lg">
                  <h5 className="text-white font-bold mb-5 text-xl">Time-Limited Benefits</h5>
                  <div className="space-y-4">
                    {timeFeatures.map((feature, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300">
                        <span className="text-white text-lg font-semibold">{feature.text}</span>
                        <span className={`${tierText} text-base font-bold px-4 py-2 rounded-xl bg-gray-800/80 border-2 ${tierBorder} shadow-lg`}>
                          {feature.duration}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Peds Tab */}
          {activeTab === 'peds' && peds.length > 0 && (
            <div>
              <h5 className="text-white font-semibold mb-6 text-lg">Character Models ({peds.length})</h5>
              <div className="grid grid-cols-3 gap-4">
                {peds.map((ped, index) => (
                  <div key={index} className="group relative">
                    <div className="aspect-square rounded-xl overflow-hidden bg-gray-800/50 border border-gray-700/50 group-hover:border-gray-600 transition-all duration-300 shadow-lg">
                      <Image
                        src={ped.image}
                        alt={ped.name}
                        width={120}
                        height={120}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="text-white text-sm font-semibold truncate text-center bg-black/90 rounded-lg px-3 py-2">{ped.name}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Clothing Tab */}
          {activeTab === 'clothing' && clothing.length > 0 && (
            <div>
              <h5 className="text-white font-semibold mb-6 text-lg">Exclusive Clothing Collection</h5>
              <div className="space-y-5">
                {getOrderedClothing().map((item, displayIndex) => {
                  // Find original index to determine the actual tier
                  const originalIndex = clothing.findIndex(originalItem => originalItem === item);
                  const itemTier = originalIndex + 1;
                  const isCurrentTier = itemTier === tier;

                  // Get rank-specific colors
                  const rankColors = getClothingRankColors(originalIndex);

                  // Generate image path based on item tier
                  const clothingImage = item.image || `/assets/vip/vip${itemTier}.png`;

                  return (
                    <div key={`${originalIndex}-${displayIndex}`} className={`group relative overflow-hidden rounded-2xl bg-gradient-to-br ${rankColors.bg} border-2 ${rankColors.border} ${rankColors.hoverBorder} transition-all duration-500 shadow-xl ${rankColors.shadow} ${isCurrentTier ? 'ring-2 ring-current/30' : ''}`}>
                      {/* Current Tier Indicator */}
                      {isCurrentTier && (
                        <div className="absolute top-2 right-2 z-20">
                          <div className={`px-2 py-1 ${rankColors.badge} rounded-lg text-xs font-bold border`}>
                            YOUR TIER
                          </div>
                        </div>
                      )}

                      <div className="relative z-10 p-5">
                        <div className="flex items-center space-x-5">
                          {/* Clothing Image */}
                          <div className="relative">
                            <div
                              className={`w-20 h-20 rounded-2xl overflow-hidden bg-gradient-to-br ${rankColors.bg} border-2 ${rankColors.border} shadow-lg cursor-pointer hover:scale-105 transition-transform duration-300 group/image`}
                              onClick={() => setSelectedImage(clothingImage)}
                            >
                              <Image
                                src={clothingImage}
                                alt={item.placeholder || item.name || `VIP ${itemTier} Clothing`}
                                width={80}
                                height={80}
                                className="w-full h-full object-contain p-2"
                              />

                              {/* Rank-specific Particles */}
                              <div className="absolute inset-0 opacity-0 group-hover/image:opacity-100 transition-opacity duration-300 pointer-events-none">
                                <div className={`absolute w-1 h-1 ${rankColors.particles} rounded-full animate-ping`} style={{top: '10%', left: '20%', animationDelay: '0s'}}></div>
                                <div className={`absolute w-0.5 h-0.5 ${rankColors.particles} rounded-full animate-pulse`} style={{top: '15%', right: '25%', animationDelay: '0.3s'}}></div>
                                <div className={`absolute w-1 h-1 ${rankColors.particles} rounded-full animate-ping`} style={{right: '15%', top: '30%', animationDelay: '0.1s'}}></div>
                                <div className={`absolute w-0.5 h-0.5 ${rankColors.particles} rounded-full animate-pulse`} style={{right: '10%', bottom: '35%', animationDelay: '0.5s'}}></div>
                                <div className={`absolute w-1 h-1 ${rankColors.particles} rounded-full animate-ping`} style={{bottom: '20%', right: '20%', animationDelay: '0.4s'}}></div>
                                <div className={`absolute w-0.5 h-0.5 ${rankColors.particles} rounded-full animate-pulse`} style={{bottom: '15%', left: '30%', animationDelay: '0.7s'}}></div>
                                <div className={`absolute w-1 h-1 ${rankColors.particles} rounded-full animate-ping`} style={{left: '10%', bottom: '25%', animationDelay: '0.2s'}}></div>
                                <div className={`absolute w-0.5 h-0.5 ${rankColors.particles} rounded-full animate-pulse`} style={{left: '15%', top: '40%', animationDelay: '0.6s'}}></div>
                              </div>
                            </div>
                          </div>

                          {/* Clothing Details */}
                          <div className="flex-grow">
                            <h6 className="text-white font-bold text-lg mb-2">
                              {item.placeholder || item.name || `[VIP ${itemTier} Clothing Name]`}
                            </h6>

                            <p className="text-gray-300 text-sm leading-relaxed">
                              {item.description}
                            </p>
                          </div>

                          {/* VIP Badge with Tier-specific Colors */}
                          <div className="flex-shrink-0">
                            <div className={`w-10 h-10 rounded-xl bg-gradient-to-br ${rankColors.bg} border-2 ${rankColors.border} flex items-center justify-center shadow-lg`}>
                              <span className={`font-bold text-sm ${rankColors.badge.split(' ')[2]}`}>V{itemTier}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Special VIP 5 Full Outfit Card */}
                {tier === 5 && (
                  <div className="group relative overflow-hidden rounded-3xl bg-gradient-to-br from-yellow-500/30 via-yellow-400/20 to-amber-500/25 border-2 border-yellow-400/60 hover:border-yellow-300/80 transition-all duration-500 shadow-2xl hover:shadow-yellow-400/40">
                    {/* Animated Background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 via-transparent to-yellow-300/10 animate-pulse"></div>
                    <div className="absolute inset-0 opacity-30">
                      <div className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping" style={{top: '10%', left: '15%', animationDelay: '0s'}}></div>
                      <div className="absolute w-1.5 h-1.5 bg-amber-300 rounded-full animate-pulse" style={{top: '20%', right: '20%', animationDelay: '0.5s'}}></div>
                      <div className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping" style={{bottom: '25%', left: '10%', animationDelay: '1s'}}></div>
                      <div className="absolute w-1.5 h-1.5 bg-amber-300 rounded-full animate-pulse" style={{bottom: '15%', right: '15%', animationDelay: '1.5s'}}></div>
                    </div>

                    <div className="relative z-10 p-4 sm:p-5 md:p-6">
                      <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 md:space-x-6">
                        {/* Full Outfit Image */}
                        <div className="relative flex-shrink-0 mx-auto sm:mx-0">
                          <div
                            className="w-28 h-44 sm:w-32 sm:h-52 md:w-36 md:h-60 rounded-3xl overflow-hidden bg-gradient-to-br from-yellow-400/40 to-amber-500/30 border-3 border-yellow-400/60 shadow-xl cursor-pointer hover:scale-105 transition-all duration-300 group/fullimage hover:shadow-yellow-400/50"
                            onClick={() => setSelectedImage('/assets/vip/full-vip5.png')}
                          >
                            <Image
                              src="/assets/vip/full-vip5.png"
                              alt="VIP 5 Complete Outfit Preview"
                              width={144}
                              height={240}
                              className="w-full h-full object-contain p-1"
                            />

                            {/* Special Particles for Full Outfit */}
                            <div className="absolute inset-0 opacity-0 group-hover/fullimage:opacity-100 transition-opacity duration-300 pointer-events-none">
                              <div className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping" style={{top: '8%', left: '15%', animationDelay: '0s'}}></div>
                              <div className="absolute w-1 h-1 bg-yellow-300 rounded-full animate-pulse" style={{top: '12%', right: '20%', animationDelay: '0.2s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-ping" style={{right: '10%', top: '25%', animationDelay: '0.1s'}}></div>
                              <div className="absolute w-1 h-1 bg-yellow-300 rounded-full animate-pulse" style={{right: '15%', top: '40%', animationDelay: '0.4s'}}></div>
                              <div className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping" style={{right: '12%', top: '55%', animationDelay: '0.3s'}}></div>
                              <div className="absolute w-1 h-1 bg-yellow-300 rounded-full animate-pulse" style={{right: '18%', bottom: '30%', animationDelay: '0.6s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-ping" style={{bottom: '15%', right: '15%', animationDelay: '0.5s'}}></div>
                              <div className="absolute w-1 h-1 bg-yellow-300 rounded-full animate-pulse" style={{bottom: '10%', left: '25%', animationDelay: '0.8s'}}></div>
                              <div className="absolute w-2 h-2 bg-yellow-400 rounded-full animate-ping" style={{left: '8%', bottom: '20%', animationDelay: '0.4s'}}></div>
                              <div className="absolute w-1 h-1 bg-yellow-300 rounded-full animate-pulse" style={{left: '12%', top: '35%', animationDelay: '0.7s'}}></div>
                            </div>
                          </div>
                        </div>

                        {/* Full Outfit Details */}
                        <div className="flex-grow text-center sm:text-left">
                          <div className="flex flex-col sm:flex-row items-center sm:items-start space-y-2 sm:space-y-0 sm:space-x-3 mb-3">
                            <h6 className="text-yellow-300 font-bold text-lg sm:text-xl">
                              Complete VIP 5 Outfit Preview
                            </h6>
                            <div className="px-2 sm:px-3 py-1 bg-yellow-400/20 border border-yellow-400/40 rounded-lg">
                              <span className="text-yellow-300 text-xs sm:text-sm font-bold">EXCLUSIVE</span>
                            </div>
                          </div>

                          <p className="text-gray-300 text-xs sm:text-sm leading-relaxed mb-3 sm:mb-4">
                            See how the complete VIP 5 outfit looks when worn together. This exclusive preview shows the full styling combination of all VIP tier clothing items.
                          </p>

                          <div className="flex items-center justify-center sm:justify-start space-x-2">
                            <i className="fas fa-crown text-yellow-400 text-sm"></i>
                            <span className="text-yellow-300 text-xs sm:text-sm font-semibold">Ultimate VIP Experience</span>
                          </div>
                        </div>

                        {/* Special VIP 5 Badge */}
                        <div className="flex-shrink-0 mx-auto sm:mx-0 sm:mr-2">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-gradient-to-br from-yellow-400/40 to-yellow-300/30 border-2 border-yellow-400/50 flex items-center justify-center shadow-lg">
                            <span className="text-yellow-300 font-bold text-base sm:text-lg">👑</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700/30 bg-gray-900/20 flex justify-center">
          <button
            className={`relative overflow-hidden px-6 py-2 rounded-lg font-bold text-base transition-all duration-300 ${
              isSpecialTier
                ? `bg-gradient-to-r ${tierGradient} ${tierText} border-2 ${tierBorder} hover:shadow-lg hover:shadow-current/40`
                : 'bg-gray-800/80 text-white border-2 border-gray-600/50 hover:bg-gray-700/80 hover:border-gray-500/70'
            } hover:scale-110 flex items-center justify-center gap-2 group shadow-md`}
            onMouseEnter={() => {
              if (tier === 4 || tier === 5) {
                setIsHovered(true);
              }
            }}
            onMouseLeave={() => {
              if (tier === 4 || tier === 5) {
                setIsHovered(false);
              }
            }}
            onClick={() => window.open('https://discord.gg/GAMravHDnB', '_blank')}
          >
            <span className="text-base">🎫</span>
            <span>Grab Now</span>

            {/* VIP 4 Violet Glow Effect */}
            {tier === 4 && isHovered && (
              <div className="absolute inset-0 bg-violet-500/30 rounded-lg animate-pulse pointer-events-none"></div>
            )}

            {/* VIP 5 Shimmer Effect */}
            {tier === 5 && isHovered && (
              <>
                <div className="absolute inset-0 bg-yellow-400/30 rounded-lg animate-pulse pointer-events-none"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-300/50 to-transparent -skew-x-12 translate-x-[-100%] animate-shimmer pointer-events-none"></div>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-md z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative w-full max-w-lg max-h-[90vh] bg-gradient-to-br from-gray-900/95 to-gray-800/95 rounded-3xl border-2 border-gray-600/50 shadow-2xl overflow-hidden">
            <div className="p-4 sm:p-6">
              <div className="relative">
                <div className="w-full h-auto max-h-[80vh] flex items-center justify-center rounded-2xl bg-gradient-to-br from-gray-800/60 to-gray-700/60 overflow-hidden">
                  <Image
                    src={selectedImage}
                    alt="Clothing Item Preview"
                    width={500}
                    height={600}
                    className="max-w-full max-h-full object-contain"
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>

                {/* Close Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedImage(null);
                  }}
                  className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 w-8 h-8 sm:w-10 sm:h-10 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white transition-all duration-200 shadow-xl hover:scale-110 z-20"
                >
                  <i className="fas fa-times text-sm sm:text-base"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VIPCard;
