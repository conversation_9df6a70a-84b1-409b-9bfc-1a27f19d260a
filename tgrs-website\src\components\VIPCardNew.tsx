'use client';

import { useState } from 'react';
import Image from 'next/image';
import Button from './Button';

interface Feature {
  text: string;
  details?: string;
  highlight?: string;
  example?: string;
}

interface TimeFeature {
  text: string;
  duration: string;
}

interface Ped {
  name: string;
  image: string;
}

interface ClothingItem {
  name: string;
  description: string;
  variants: number;
  image?: string; // Optional image path for clothing item
  placeholder?: string; // Placeholder text for clothing name
}

interface VIPCardProps {
  tier: number;
  price: number;
  title?: string;
  description: string;
  icon: string;
  mainFeatures: Feature[];
  timeFeatures: TimeFeature[];
  clothing: ClothingItem[];
  peds: Ped[];
  isPopular?: boolean;
  tiltDirection?: 'left' | 'right';
  pattern?: string;
  className?: string;
  currency?: string;
}

const VIPCard = ({
  tier,
  price,
  title,
  description,
  icon,
  mainFeatures,
  timeFeatures,
  clothing,
  peds,
  isPopular = false,
  className = "",
  currency = "₹"
}: VIPCardProps) => {
  const [activeTab, setActiveTab] = useState<'features' | 'peds' | 'clothing'>('features');
  const [isHovered, setIsHovered] = useState(false);

  // Get tier-specific styling and icons
  let tierBorder = "border-orange-500";
  let tierText = "text-orange-400";
  let tierAccent = "bg-orange-500";
  let tierGradient = "from-orange-500/30 to-yellow-500/30";
  let tierGlow = "shadow-orange-500/30";
  let tierIcon = "fas fa-star";
  let isSpecialTier = false;

  if (tier === 1) {
    // Maroon color
    tierBorder = "border-red-800";
    tierText = "text-red-300";
    tierAccent = "bg-red-800";
    tierGradient = "from-red-800/30 to-red-700/30";
    tierGlow = "shadow-red-800/30";
    tierIcon = "fas fa-medal";
  } else if (tier === 2) {
    // Silver color
    tierBorder = "border-gray-400";
    tierText = "text-gray-300";
    tierAccent = "bg-gray-400";
    tierGradient = "from-gray-400/30 to-gray-300/30";
    tierGlow = "shadow-gray-400/30";
    tierIcon = "fas fa-award";
  } else if (tier === 3) {
    // Bronze color
    tierBorder = "border-amber-600";
    tierText = "text-amber-400";
    tierAccent = "bg-amber-600";
    tierGradient = "from-amber-600/30 to-amber-500/30";
    tierGlow = "shadow-amber-600/30";
    tierIcon = "fas fa-trophy";
  } else if (tier === 4) {
    // Violet color
    tierBorder = "border-violet-500";
    tierText = "text-violet-400";
    tierAccent = "bg-violet-500";
    tierGradient = "from-violet-500/30 to-violet-400/30";
    tierGlow = "shadow-violet-500/30";
    tierIcon = "fas fa-gem";
    isSpecialTier = true;
  } else if (tier === 5) {
    // Golden color (brighter)
    tierBorder = "border-yellow-400";
    tierText = "text-yellow-300";
    tierAccent = "bg-yellow-400";
    tierGradient = "from-yellow-400/30 to-yellow-300/30";
    tierGlow = "shadow-yellow-400/30";
    tierIcon = "fas fa-crown";
    isSpecialTier = true;
  }

  return (
    <div className={`relative ${className} group`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-2 right-4 z-30">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 px-3 py-1 rounded-md text-black text-xs font-bold shadow-lg">
            POPULAR
          </div>
        </div>
      )}

      {/* Card Glow Effect - All Cards */}
      <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${tierGradient} opacity-20 blur-xl pointer-events-none`}></div>

      {/* Enhanced Glow for Special Tiers */}
      {isSpecialTier && (
        <div className={`absolute inset-0 rounded-3xl bg-gradient-to-r ${tierGradient} opacity-40 blur-2xl pointer-events-none animate-pulse`}></div>
      )}

      <div className={`bg-black/95 backdrop-blur-sm rounded-3xl border-2 ${tierBorder} hover:border-opacity-100 transition-all duration-500 overflow-hidden relative h-full flex flex-col shadow-xl ${tierGlow} group-hover:shadow-2xl group-hover:${tierGlow.replace('/30', '/50')}`}>

        {/* Header */}
        <div className="p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-900/50 to-black/50 relative overflow-hidden">
          {/* Shine Effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-1000 ease-out"></div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${tierGradient} border-2 ${tierBorder} flex items-center justify-center ${isSpecialTier ? 'animate-pulse shadow-lg shadow-current/50' : 'shadow-lg'}`}>
                <i className={`${tierIcon} ${tierText} text-2xl`}></i>
              </div>
              <div>
                <h3 className="text-3xl font-bold text-white mb-1">{title || `VIP ${tier}`}</h3>
                <p className="text-gray-300 text-lg font-medium">{description}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-5xl font-bold text-white mb-1">{currency}{price.toLocaleString()}</div>
              <div className="text-gray-400 text-base font-medium">One-time purchase</div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="px-8 py-4 border-b border-gray-700/30 bg-gray-900/30">
          <div className="flex space-x-3">
            <button
              onClick={() => setActiveTab('features')}
              className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${
                activeTab === 'features'
                  ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`
                  : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'
              }`}
            >
              Features
            </button>
            {peds.length > 0 && (
              <button
                onClick={() => setActiveTab('peds')}
                className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${
                  activeTab === 'peds'
                    ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'
                }`}
              >
                Peds ({peds.length})
              </button>
            )}
            {clothing.length > 0 && (
              <button
                onClick={() => setActiveTab('clothing')}
                className={`flex-1 px-4 py-3 rounded-xl text-sm font-bold transition-all duration-300 text-center whitespace-nowrap ${
                  activeTab === 'clothing'
                    ? `${tierText} bg-gray-800/90 shadow-lg border-2 border-current/40`
                    : 'text-gray-400 hover:text-white hover:bg-gray-800/50 border-2 border-transparent'
                }`}
              >
                Clothing
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-8 flex-grow">
          {/* Features Tab */}
          {activeTab === 'features' && (
            <div className="space-y-5">
              {mainFeatures.map((feature, index) => (
                <div key={index} className="flex items-start space-x-4 p-5 rounded-2xl bg-gray-800/40 border border-gray-700/40 hover:bg-gray-800/60 hover:border-gray-600/60 transition-all duration-300 shadow-lg">
                  <div className={`w-3 h-3 rounded-full ${tierAccent} mt-3 flex-shrink-0 shadow-lg`}></div>
                  <div className="flex-grow">
                    <div className="text-white font-bold text-lg mb-3">{feature.text}</div>
                    {feature.details && (
                      <div className="text-gray-300 text-base mb-4">{feature.details}</div>
                    )}
                    <div className="flex flex-wrap gap-3">
                      {feature.highlight && (
                        <span className={`${tierText} text-base font-bold bg-gray-800/80 px-4 py-2 rounded-xl border-2 ${tierBorder} shadow-lg`}>
                          {feature.highlight}
                        </span>
                      )}
                      {feature.example && (
                        <span className="text-gray-200 text-base font-mono bg-gray-900/80 px-4 py-2 rounded-xl border border-gray-600 shadow-lg">
                          {feature.example}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* Time Features */}
              {timeFeatures.length > 0 && (
                <div className="mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/40 shadow-lg">
                  <h5 className="text-white font-bold mb-5 text-xl">Time-Limited Benefits</h5>
                  <div className="space-y-4">
                    {timeFeatures.map((feature, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-800/50 rounded-xl border border-gray-700/50 hover:bg-gray-800/70 transition-all duration-300">
                        <span className="text-white text-lg font-semibold">{feature.text}</span>
                        <span className={`${tierText} text-base font-bold px-4 py-2 rounded-xl bg-gray-800/80 border-2 ${tierBorder} shadow-lg`}>
                          {feature.duration}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Peds Tab */}
          {activeTab === 'peds' && peds.length > 0 && (
            <div>
              <h5 className="text-white font-semibold mb-6 text-lg">Character Models ({peds.length})</h5>
              <div className="grid grid-cols-3 gap-4">
                {peds.map((ped, index) => (
                  <div key={index} className="group relative">
                    <div className="aspect-square rounded-xl overflow-hidden bg-gray-800/50 border border-gray-700/50 group-hover:border-gray-600 transition-all duration-300 shadow-lg">
                      <Image
                        src={ped.image}
                        alt={ped.name}
                        width={120}
                        height={120}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="text-white text-sm font-semibold truncate text-center bg-black/90 rounded-lg px-3 py-2">{ped.name}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Clothing Tab */}
          {activeTab === 'clothing' && clothing.length > 0 && (
            <div>
              <h5 className="text-white font-semibold mb-6 text-lg">Exclusive Clothing Collection</h5>
              <div className="space-y-5">
                {clothing.map((item, index) => {
                  // Generate image path based on VIP tier
                  const clothingImage = item.image || `/assets/vip/vip${tier}.png`;

                  return (
                    <div key={index} className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-800/40 to-gray-900/60 border border-gray-700/40 hover:border-current/50 transition-all duration-500 shadow-xl hover:shadow-current/20">
                      {/* Background Pattern */}
                      <div className="absolute inset-0 opacity-5">
                        <div className={`absolute inset-0 bg-gradient-to-br ${tierGradient}`}></div>
                        <div className="absolute inset-0" style={{
                          backgroundImage: `radial-gradient(circle at 25% 25%, currentColor 0%, transparent 50%),
                                           radial-gradient(circle at 75% 75%, currentColor 0%, transparent 50%)`
                        }}></div>
                      </div>

                      <div className="relative z-10 p-5">
                        <div className="flex items-center space-x-5">
                          {/* Clothing Image */}
                          <div className="relative">
                            <div className={`w-20 h-20 rounded-2xl overflow-hidden bg-gradient-to-br ${tierGradient} border-2 ${tierBorder} group-hover:border-opacity-80 transition-all duration-500 shadow-lg group-hover:shadow-current/30`}>
                              <Image
                                src={clothingImage}
                                alt={item.placeholder || item.name || `VIP ${tier} Clothing`}
                                width={80}
                                height={80}
                                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                              />
                              {/* Overlay glow effect */}
                              <div className={`absolute inset-0 bg-gradient-to-t ${tierGradient} opacity-0 group-hover:opacity-30 transition-opacity duration-500`}></div>
                            </div>

                            {/* Floating particles effect */}
                            <div className={`absolute -top-1 -right-1 w-2 h-2 ${tierAccent} rounded-full opacity-0 group-hover:opacity-100 animate-ping transition-opacity duration-500`}></div>
                            <div className={`absolute -bottom-1 -left-1 w-1.5 h-1.5 ${tierAccent} rounded-full opacity-0 group-hover:opacity-100 animate-pulse transition-opacity duration-700`}></div>
                          </div>

                          {/* Clothing Details */}
                          <div className="flex-grow">
                            <div className="mb-2">
                              <h6 className={`text-white font-bold text-lg mb-1 group-hover:${tierText} transition-colors duration-300`}>
                                {item.placeholder || item.name || `[Clothing Name Placeholder]`}
                              </h6>
                              {item.placeholder && (
                                <p className={`${tierText} text-xs italic opacity-70`}>
                                  * Placeholder - Add actual clothing name here
                                </p>
                              )}
                            </div>

                            <p className="text-gray-300 text-sm mb-3 group-hover:text-gray-200 transition-colors duration-300 leading-relaxed">
                              {item.description}
                            </p>

                            {item.variants > 0 && (
                              <div className="flex items-center space-x-2">
                                <span className={`${tierText} font-semibold text-sm flex items-center`}>
                                  <i className="fas fa-palette mr-1 text-xs"></i>
                                  {item.variants} variants
                                </span>
                                <div className="flex space-x-1">
                                  {Array.from({ length: Math.min(item.variants, 4) }).map((_, i) => (
                                    <div
                                      key={i}
                                      className={`w-2 h-2 rounded-full ${tierAccent} shadow-sm group-hover:shadow-current/50 transition-shadow duration-300`}
                                      style={{ animationDelay: `${i * 0.1}s` }}
                                    ></div>
                                  ))}
                                  {item.variants > 4 && (
                                    <span className={`${tierText} text-xs ml-1 font-medium opacity-80`}>
                                      +{item.variants - 4}
                                    </span>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>

                          {/* VIP Badge */}
                          <div className="flex-shrink-0">
                            <div className={`w-10 h-10 rounded-xl bg-gradient-to-br ${tierGradient} border ${tierBorder} flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                              <span className={`${tierText} font-bold text-sm`}>V{tier}</span>
                            </div>
                          </div>
                        </div>

                        {/* Bottom accent line */}
                        <div className={`absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-current to-transparent transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 ${tierText}`}></div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-700/30 bg-gray-900/20 flex justify-center">
          <button
            className={`relative overflow-hidden px-6 py-2 rounded-lg font-bold text-base transition-all duration-300 ${
              isSpecialTier
                ? `bg-gradient-to-r ${tierGradient} ${tierText} border-2 ${tierBorder} hover:shadow-lg hover:shadow-current/40`
                : 'bg-gray-800/80 text-white border-2 border-gray-600/50 hover:bg-gray-700/80 hover:border-gray-500/70'
            } hover:scale-110 flex items-center justify-center gap-2 group shadow-md`}
            onMouseEnter={() => {
              if (tier === 4 || tier === 5) {
                setIsHovered(true);
              }
            }}
            onMouseLeave={() => {
              if (tier === 4 || tier === 5) {
                setIsHovered(false);
              }
            }}
            onClick={() => window.open('https://discord.gg/GAMravHDnB', '_blank')}
          >
            <span className="text-base">🎫</span>
            <span>Grab Now</span>

            {/* VIP 4 Violet Glow Effect */}
            {tier === 4 && isHovered && (
              <div className="absolute inset-0 bg-violet-500/30 rounded-lg animate-pulse pointer-events-none"></div>
            )}

            {/* VIP 5 Shimmer Effect */}
            {tier === 5 && isHovered && (
              <>
                <div className="absolute inset-0 bg-yellow-400/30 rounded-lg animate-pulse pointer-events-none"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-yellow-300/50 to-transparent -skew-x-12 translate-x-[-100%] animate-shimmer pointer-events-none"></div>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default VIPCard;
