"use client";

import React, { useState } from "react";
import Button from "./Button";
import Image from "next/image";

interface VIPFeature {
  text: string;
  details?: string;
  highlight?: string;
  example?: string;
}

interface TimeFeature {
  text: string;
  duration: string;
}

interface ClothingItem {
  name: string;
  variants: number;
  description: string;
  image?: string; // Optional image path for clothing item
  placeholder?: string; // Placeholder text for clothing name
}

interface PedItem {
  name: string;
  image: string;
}

interface PetItem {
  name: string;
  image: string;
}

interface VIPCardProps {
  tier: number;
  price: number;
  currency?: string;
  title?: string;
  description?: string;
  icon?: string;
  mainFeatures: VIPFeature[];
  timeFeatures?: TimeFeature[];
  clothing?: ClothingItem[];
  peds?: PedItem[];
  pets?: PetItem[];
  isPopular?: boolean;
  className?: string;
  tiltDirection?: "left" | "right" | "none";
  pattern?: "dots" | "grid" | "hexagon" | "circuit" | "none";
  contactInfo?: string;
}

const VIPCard = ({
  tier,
  price,
  currency = "₹",
  title,
  description,
  icon,
  mainFeatures,
  timeFeatures = [],
  clothing = [],
  peds = [],
  pets = [],
  isPopular = false,
  className = "",
  tiltDirection = "none",
  pattern = "circuit",
  contactInfo = "Contact admin for purchase"
}: VIPCardProps) => {
  const [activeTab, setActiveTab] = useState<'features' | 'peds' | 'clothing'>('features');
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Get tier-specific styling and icons
  let tierBorder = "border-neon-orange/30";
  let tierText = "text-neon-orange";
  let tierAccent = "bg-neon-orange";
  let tierPrimary = "from-neon-orange to-yellow-500";
  let tierSecondary = "from-neon-orange/20 to-yellow-500/10";
  let tierIcon = "fas fa-star";
  let isSpecialTier = false;

  if (tier === 1) {
    tierBorder = "border-blue-500/30";
    tierText = "text-blue-400";
    tierAccent = "bg-blue-500";
    tierPrimary = "from-blue-500 to-blue-600";
    tierSecondary = "from-blue-500/20 to-blue-600/10";
    tierIcon = "fas fa-medal";
  } else if (tier === 2) {
    tierBorder = "border-purple-500/30";
    tierText = "text-purple-400";
    tierAccent = "bg-purple-500";
    tierPrimary = "from-purple-500 to-purple-600";
    tierSecondary = "from-purple-500/20 to-purple-600/10";
    tierIcon = "fas fa-award";
  } else if (tier === 3) {
    tierIcon = "fas fa-trophy";
  } else if (tier === 4) {
    tierBorder = "border-red-500/30";
    tierText = "text-red-400";
    tierAccent = "bg-red-500";
    tierPrimary = "from-red-500 to-red-600";
    tierSecondary = "from-red-500/20 to-red-600/10";
    tierIcon = "fas fa-gem";
    isSpecialTier = true;
  } else if (tier === 5) {
    tierBorder = "border-yellow-400/30";
    tierText = "text-yellow-400";
    tierAccent = "bg-yellow-400";
    tierPrimary = "from-yellow-400 to-yellow-500";
    tierSecondary = "from-yellow-400/20 to-yellow-500/10";
    tierIcon = "fas fa-crown";
    isSpecialTier = true;
  }

  return (
    <div className={`relative ${className} group`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-30">
          <div className="relative flex items-center">
            {/* Left Border Cut */}
            <div className="w-12 h-px bg-gradient-to-r from-transparent via-yellow-400 to-yellow-500 mr-2"></div>

            {/* Main Badge */}
            <div className="relative">
              {/* Badge Background with Notched Corners */}
              <div className="bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 px-6 py-2 relative popular-badge-shape shadow-2xl">
                {/* Shimmer Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-transparent -skew-x-12 translate-x-[-100%] animate-shimmer popular-badge-shape"></div>

                {/* Badge Content */}
                <div className="relative z-10 flex items-center space-x-1">
                  <span className="text-yellow-900 text-xs">⭐</span>
                  <span className="text-black font-black text-xs tracking-widest uppercase">MOST POPULAR</span>
                  <span className="text-yellow-900 text-xs">⭐</span>
                </div>
              </div>

              {/* Glow Effect */}
              <div className="absolute inset-0 bg-yellow-400 blur-md opacity-60 animate-pulse popular-badge-shape"></div>
            </div>

            {/* Right Border Cut */}
            <div className="w-12 h-px bg-gradient-to-l from-transparent via-yellow-400 to-yellow-500 ml-2"></div>
          </div>
        </div>
      )}

      {/* Special Tier Effects for VIP 4 & 5 */}
      {isSpecialTier && (
        <>
          {/* Floating particles around the card */}
          <div className="absolute -inset-4 opacity-60 pointer-events-none">
            <div className={`absolute top-2 left-2 w-2 h-2 ${tierAccent} rounded-full animate-ping`}></div>
            <div className={`absolute top-8 right-4 w-1.5 h-1.5 ${tierAccent} rounded-full animate-pulse`} style={{animationDelay: '0.5s'}}></div>
            <div className={`absolute bottom-6 left-6 w-1 h-1 ${tierAccent} rounded-full animate-bounce`} style={{animationDelay: '0.3s'}}></div>
            <div className={`absolute bottom-2 right-2 w-2 h-2 ${tierAccent} rounded-full animate-ping`} style={{animationDelay: '0.7s'}}></div>
            <div className={`absolute top-1/2 left-1 w-1 h-1 ${tierAccent} rounded-full animate-pulse`} style={{animationDelay: '0.2s'}}></div>
            <div className={`absolute top-1/3 right-1 w-1.5 h-1.5 ${tierAccent} rounded-full animate-bounce`} style={{animationDelay: '0.8s'}}></div>
          </div>

          {/* Glowing border effect */}
          <div className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${tierPrimary} opacity-20 blur-sm animate-pulse pointer-events-none`}></div>
        </>
      )}

      <div className={`glass-enhanced rounded-2xl border-2 ${tierBorder} hover:border-neon-orange/70 transition-all duration-500 overflow-hidden relative bg-gradient-to-br ${tierSecondary} backdrop-blur-xl h-full flex flex-col ${isSpecialTier ? 'shadow-2xl' : ''}`}>
        {/* Header Section */}
        <div className={`bg-gradient-to-r ${tierPrimary} p-8 text-center relative overflow-hidden`}>
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.1%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%222%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
          </div>

          {/* VIP Badge */}
          <div className="relative z-10">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full bg-white/20 backdrop-blur-sm border-2 border-white/30 mb-6 ${isSpecialTier ? 'animate-pulse' : ''}`}>
              <i className={`${tierIcon} text-white text-2xl ${isSpecialTier ? 'animate-bounce' : ''}`}></i>
            </div>

            <h3 className="text-3xl font-display font-bold text-white mb-3 flex items-center justify-center">
              <span className="mr-2">{title || `VIP ${tier}`}</span>
              {isSpecialTier && <i className={`${tierIcon} text-xl ${tierText} animate-pulse`}></i>}
            </h3>

            <div className="text-5xl font-display font-bold text-white mb-3">
              {currency}{price.toLocaleString()}
            </div>

            <div className="text-white/80 text-base font-medium">
              {isSpecialTier ? "🌟 Premium Elite Package 🌟" : "One-time purchase"}
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex border-b border-white/10">
          <button
            onClick={() => setActiveTab('features')}
            className={`flex-1 py-4 px-6 text-base font-semibold transition-colors duration-300 ${
              activeTab === 'features'
                ? `${tierText} border-b-2 border-current bg-white/5`
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <i className="fas fa-star mr-2 text-lg"></i>
            Features
          </button>

          {peds.length > 0 && (
            <button
              onClick={() => setActiveTab('peds')}
              className={`flex-1 py-4 px-6 text-base font-semibold transition-colors duration-300 ${
                activeTab === 'peds'
                  ? `${tierText} border-b-2 border-current bg-white/5`
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <i className="fas fa-user mr-2 text-lg"></i>
              Peds ({peds.length})
            </button>
          )}

          {clothing.length > 0 && (
            <button
              onClick={() => setActiveTab('clothing')}
              className={`flex-1 py-4 px-6 text-base font-semibold transition-colors duration-300 ${
                activeTab === 'clothing'
                  ? `${tierText} border-b-2 border-current bg-white/5`
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <i className="fas fa-tshirt mr-2 text-lg"></i>
              Clothing
            </button>
          )}
        </div>

        {/* Content Area */}
        <div className="p-8 flex-grow flex flex-col">
          {/* Features Tab */}
          {activeTab === 'features' && (
            <div className="space-y-8 flex-grow">
              {/* Main Features */}
              <div>
                <h4 className="text-white font-bold mb-6 flex items-center text-xl">
                  <i className="fas fa-gem text-neon-orange mr-3 text-xl"></i>
                  Main Features
                </h4>
                <div className="space-y-5">
                  {mainFeatures.map((feature, index) => (
                    <div key={index} className="bg-black/30 rounded-xl p-6 border border-white/10 hover:border-white/20 hover:bg-black/40 transition-all duration-300 shadow-lg">
                      <div className="flex items-start">
                        <div className={`w-4 h-4 rounded-full ${tierAccent} mt-2 mr-4 flex-shrink-0 shadow-lg`}></div>
                        <div className="flex-grow">
                          <div className="text-white font-semibold text-lg mb-3">{feature.text}</div>
                          {feature.details && (
                            <div className="text-gray-300 text-base mt-2 mb-3">{feature.details}</div>
                          )}
                          {feature.highlight && (
                            <div className={`${tierText} text-base mt-3 font-bold bg-white/10 px-4 py-2 rounded-lg border border-current/30 inline-block shadow-md`}>
                              <i className="fas fa-info-circle mr-2"></i>
                              {feature.highlight}
                            </div>
                          )}
                          {feature.example && (
                            <div className="mt-4 relative">
                              <div className="text-gray-200 text-sm font-bold mb-2 flex items-center">
                                <i className="fas fa-code mr-2 text-neon-orange text-base"></i>
                                EXAMPLE
                              </div>
                              <div className={`${tierText} text-base font-mono bg-black/60 px-5 py-3 rounded-lg border-l-4 border-current shadow-xl`}>
                                {feature.example}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Time Features */}
              {timeFeatures.length > 0 && (
                <div>
                  <h4 className="text-white font-bold mb-6 flex items-center text-xl">
                    <i className="fas fa-clock text-neon-orange mr-3 text-xl"></i>
                    Time-Limited Benefits
                  </h4>
                  <div className="space-y-4">
                    {timeFeatures.map((feature, index) => (
                      <div key={index} className="flex items-center justify-between bg-black/30 rounded-xl p-5 border border-white/10 hover:border-white/20 hover:bg-black/40 transition-all duration-300 shadow-lg">
                        <span className="text-white text-lg font-semibold">{feature.text}</span>
                        <span className={`${tierText} text-base font-bold px-4 py-2 rounded-full bg-white/15 border border-current/40 shadow-lg`}>
                          <i className="fas fa-calendar-alt mr-2"></i>
                          {feature.duration}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Peds Tab */}
          {activeTab === 'peds' && peds.length > 0 && (
            <div className="flex-grow flex flex-col">
              <h4 className="text-white font-bold mb-6 flex items-center text-xl">
                <i className="fas fa-users text-neon-orange mr-3 text-xl"></i>
                Exclusive Character Models ({peds.length})
              </h4>
              <div className="grid grid-cols-3 gap-5">
                {peds.map((ped, index) => (
                  <div key={index} className="group relative">
                    <div className="aspect-square rounded-xl overflow-hidden bg-black/30 border border-white/10 group-hover:border-neon-orange/50 transition-all duration-300 shadow-lg">
                      <Image
                        src={ped.image}
                        alt={ped.name}
                        width={120}
                        height={120}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      <div className="absolute bottom-2 left-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="text-white text-base font-bold truncate text-center bg-black/60 rounded-lg py-1 px-2">{ped.name}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Clothing Tab */}
          {activeTab === 'clothing' && clothing.length > 0 && (
            <div className="flex-grow flex flex-col">
              <h4 className="text-white font-bold mb-8 flex items-center text-xl">
                <i className="fas fa-tshirt text-neon-orange mr-3 text-xl"></i>
                Exclusive Clothing Collection
              </h4>
              <div className="grid grid-cols-1 gap-6">
                {clothing.map((item, index) => {
                  // Generate image path based on VIP tier
                  const clothingImage = item.image || `/assets/vip/vip${tier}.png`;

                  return (
                    <div key={index} className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-black/40 to-black/60 border border-white/10 hover:border-neon-orange/50 transition-all duration-500 shadow-2xl hover:shadow-neon-orange/20">
                      <div className="relative z-10 p-6">
                        <div className="flex items-center space-x-6">
                          {/* Clothing Image */}
                          <div className="relative">
                            <div
                              className="w-24 h-24 rounded-2xl overflow-hidden bg-gradient-to-br from-neon-orange/20 to-neon-orange/5 border-2 border-neon-orange/30 shadow-lg cursor-pointer hover:scale-105 transition-transform duration-300 group/image"
                              onClick={() => setSelectedImage(clothingImage)}
                            >
                              <Image
                                src={clothingImage}
                                alt={item.placeholder || item.name || `VIP ${tier} Clothing`}
                                width={96}
                                height={96}
                                className="w-full h-full object-contain p-2"
                              />

                              {/* Particles */}
                              <div className="absolute inset-0 opacity-0 group-hover/image:opacity-100 transition-opacity duration-300 pointer-events-none">
                                <div className="absolute w-1.5 h-1.5 bg-neon-orange rounded-full animate-ping" style={{top: '10%', left: '20%', animationDelay: '0s'}}></div>
                                <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-pulse" style={{top: '15%', right: '25%', animationDelay: '0.3s'}}></div>
                                <div className="absolute w-1.5 h-1.5 bg-neon-orange rounded-full animate-ping" style={{right: '15%', top: '30%', animationDelay: '0.1s'}}></div>
                                <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-pulse" style={{right: '10%', bottom: '35%', animationDelay: '0.5s'}}></div>
                                <div className="absolute w-1.5 h-1.5 bg-neon-orange rounded-full animate-ping" style={{bottom: '20%', right: '20%', animationDelay: '0.4s'}}></div>
                                <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-pulse" style={{bottom: '15%', left: '30%', animationDelay: '0.7s'}}></div>
                                <div className="absolute w-1.5 h-1.5 bg-neon-orange rounded-full animate-ping" style={{left: '10%', bottom: '25%', animationDelay: '0.2s'}}></div>
                                <div className="absolute w-1 h-1 bg-yellow-400 rounded-full animate-pulse" style={{left: '15%', top: '40%', animationDelay: '0.6s'}}></div>
                              </div>
                            </div>
                          </div>

                          {/* Clothing Details */}
                          <div className="flex-grow">
                            <h5 className="text-white font-bold text-xl mb-3">
                              {item.placeholder || item.name || `[Clothing Name Placeholder]`}
                            </h5>

                            <p className="text-gray-300 text-base leading-relaxed">
                              {item.description}
                            </p>
                          </div>

                          {/* VIP Badge */}
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-neon-orange/30 to-yellow-500/20 border border-neon-orange/40 flex items-center justify-center shadow-lg">
                              <span className="text-neon-orange font-bold text-lg">V{tier}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Special VIP 5 Full Outfit Card */}
                {tier === 5 && (
                  <div className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-neon-orange/30 to-yellow-500/20 border-2 border-neon-orange/50 hover:border-neon-orange/80 transition-all duration-500 shadow-2xl hover:shadow-neon-orange/40">
                    <div className="relative z-10 p-6">
                      <div className="flex items-start space-x-6">
                        {/* Full Outfit Image */}
                        <div className="relative flex-shrink-0">
                          <div
                            className="w-32 h-48 rounded-2xl overflow-hidden bg-gradient-to-br from-neon-orange/30 to-yellow-500/20 border-2 border-neon-orange/40 shadow-lg cursor-pointer hover:scale-105 transition-transform duration-300 group/fullimage"
                            onClick={() => setSelectedImage('/assets/vip/vip-5full.png')}
                          >
                            <Image
                              src="/assets/vip/full-vip5.png"
                              alt="VIP 5 Complete Outfit Preview"
                              width={128}
                              height={192}
                              className="w-full h-full object-contain p-2"
                            />

                            {/* Special Particles for Full Outfit */}
                            <div className="absolute inset-0 opacity-0 group-hover/fullimage:opacity-100 transition-opacity duration-300 pointer-events-none">
                              <div className="absolute w-2 h-2 bg-neon-orange rounded-full animate-ping" style={{top: '8%', left: '15%', animationDelay: '0s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse" style={{top: '12%', right: '20%', animationDelay: '0.2s'}}></div>
                              <div className="absolute w-2 h-2 bg-neon-orange rounded-full animate-ping" style={{right: '10%', top: '25%', animationDelay: '0.1s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse" style={{right: '15%', top: '40%', animationDelay: '0.4s'}}></div>
                              <div className="absolute w-2 h-2 bg-neon-orange rounded-full animate-ping" style={{right: '12%', top: '55%', animationDelay: '0.3s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse" style={{right: '18%', bottom: '30%', animationDelay: '0.6s'}}></div>
                              <div className="absolute w-2 h-2 bg-neon-orange rounded-full animate-ping" style={{bottom: '15%', right: '15%', animationDelay: '0.5s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse" style={{bottom: '10%', left: '25%', animationDelay: '0.8s'}}></div>
                              <div className="absolute w-2 h-2 bg-neon-orange rounded-full animate-ping" style={{left: '8%', bottom: '20%', animationDelay: '0.4s'}}></div>
                              <div className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse" style={{left: '12%', top: '35%', animationDelay: '0.7s'}}></div>
                            </div>
                          </div>
                        </div>

                        {/* Full Outfit Details */}
                        <div className="flex-grow">
                          <div className="flex items-center space-x-3 mb-3">
                            <h5 className="text-neon-orange font-bold text-2xl">
                              Complete VIP 5 Outfit Preview
                            </h5>
                            <div className="px-3 py-1 bg-neon-orange/20 border border-neon-orange/40 rounded-lg">
                              <span className="text-neon-orange text-sm font-bold">EXCLUSIVE</span>
                            </div>
                          </div>

                          <p className="text-gray-300 text-base leading-relaxed mb-4">
                            See how the complete VIP 5 outfit looks when worn together. This exclusive preview shows the full styling combination of all VIP tier clothing items.
                          </p>

                          <div className="flex items-center space-x-2">
                            <i className="fas fa-crown text-neon-orange text-lg"></i>
                            <span className="text-neon-orange text-base font-semibold">Ultimate VIP Experience</span>
                          </div>
                        </div>

                        {/* Special VIP 5 Badge */}
                        <div className="flex-shrink-0">
                          <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-neon-orange/40 to-yellow-500/30 border-2 border-neon-orange/50 flex items-center justify-center shadow-lg">
                            <span className="text-neon-orange font-bold text-2xl">👑</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-8 border-t border-white/10 bg-black/30">
          <Button
            variant={isPopular ? "primary" : "outline"}
            size="lg"
            className="w-full group-hover:scale-105 transition-transform duration-300 text-lg font-bold py-4"
            href="https://discord.gg/GAMravHDnB"
          >
            <i className="fas fa-crown mr-3 text-xl"></i>
            Purchase VIP {tier}
          </Button>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setSelectedImage(null)}
        >
          <div className="relative max-w-md max-h-[80vh] bg-gray-900/90 rounded-2xl border border-gray-700/50 shadow-2xl">
            <div className="p-4">
              <div className="relative">
                <Image
                  src={selectedImage}
                  alt="Clothing Item Preview"
                  width={400}
                  height={400}
                  className="w-full h-auto object-contain rounded-xl"
                />

                {/* Close Button */}
                <button
                  onClick={() => setSelectedImage(null)}
                  className="absolute -top-2 -right-2 w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white transition-colors duration-200 shadow-lg"
                >
                  <i className="fas fa-times text-sm"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default VIPCard;
