import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import VIPCard from "@/components/VIPCardNew";
import CashFlyingAnimation from "@/components/CashFlyingAnimation";
import Button from "@/components/Button";
import { <PERSON>ada<PERSON> } from "next";

export const metadata: Metadata = {
  title: "VIP Packages - TGRS Telugu FiveM Server | Premium Gaming Experience",
  description: "Unlock premium features with TGRS VIP packages! Get custom cars, priority queue, exclusive clothing, and more. Best VIP benefits for Telugu FiveM players. Join the elite gaming community today!",
  keywords: [
    // VIP Telugu Keywords
    "TGRS VIP", "FiveM VIP packages", "Telugu gaming VIP", "FiveM premium features",
    "VIP benefits FiveM", "Telugu server VIP", "gaming VIP membership", "FiveM priority queue",
    "Telugu VIP", "VIP Telugu", "Telugu VIP server", "VIP Telugu server",

    // Premium Features
    "custom cars FiveM", "exclusive gaming features", "premium gaming experience",
    "Telugu gaming premium", "VIP roleplay features", "FiveM VIP perks",
    "premium Telugu gaming", "VIP Telugu gaming", "Telugu premium server",

    // GTA VIP Keywords
    "GTA 5 VIP Telugu", "Telugu GTA 5 VIP", "GTA VIP Telugu", "Telugu GTA VIP",
    "GTA 5 premium Telugu", "Telugu GTA 5 premium", "GTA roleplay VIP Telugu",

    // SAMP VIP Keywords
    "SAMP VIP Telugu", "Telugu SAMP VIP", "SAMP premium Telugu", "Telugu SAMP premium",

    // Server VIP Keywords
    "Telugu server VIP", "VIP Telugu server", "Telugu gaming VIP membership",
    "VIP Telugu gaming membership", "Telugu community VIP", "VIP Telugu community",

    // Regional VIP Keywords
    "Hyderabad gaming VIP", "Telangana gaming VIP", "Andhra Pradesh gaming VIP",
    "South India gaming VIP", "Indian FiveM VIP", "Telugu esports VIP",

    // Feature Keywords
    "Telugu VIP features", "VIP Telugu features", "Telugu premium features",
    "premium Telugu features", "Telugu VIP benefits", "VIP Telugu benefits",
    "Telugu VIP perks", "VIP Telugu perks", "Telugu VIP packages",

    // Cultural VIP Keywords
    "Telugu culture VIP", "VIP Telugu culture", "Telugu tradition VIP",
    "VIP Telugu tradition", "Telugu language VIP", "VIP Telugu language"
  ],
  openGraph: {
    title: "VIP Packages - TGRS Telugu FiveM Server | Premium Gaming Experience",
    description: "Unlock premium features with TGRS VIP packages! Get custom cars, priority queue, exclusive clothing, and more. Best VIP benefits for Telugu FiveM players.",
    images: ["/assets/vip-og-image.jpg"],
  },
  alternates: {
    canonical: "/vip",
  },
};

export default function VIPPage() {
  const vipTiers = [
    {
      tier: 1,
      price: 299,
      title: "VIP 1",
      description: "Start your premium journey",
      icon: "fas fa-medal",
      mainFeatures: [
        {
          text: "Custom Phone Number",
          details: "with 9 digits",
          highlight: "(3 number sequence max)",
          example: "111888222",
        },
        {
          text: "Custom Car",
          details: "from TGRS category",
          highlight: "(< ₹10,00,000)",
        },
        {
          text: "In-game Cash",
          details: "₹20,000",
        },
        {
          text: "Lucky Wheel Coins",
          details: "500",
        },
      ],
      timeFeatures: [
        { text: "VIP Role", duration: "1 month" },
        { text: "Priority Queue", duration: "1 month" },
      ],
      clothing: [
        {
          name: "Yeezes",
          variants: 5,
          description: "Premium footwear with unique design and exclusive VIP styling",
          placeholder: "[Add VIP 1 Clothing Name Here]",
        },
      ],
      peds: [],
      pets: [],
      tiltDirection: "left" as const,
      pattern: "circuit" as const,
    },
    {
      tier: 2,
      price: 699,
      title: "VIP 2",
      description: "Enhanced premium experience",
      icon: "fas fa-award",
      mainFeatures: [
        {
          text: "Custom Phone Number",
          details: "with 8 digits",
          highlight: "(4 number sequence max)",
          example: "44443333",
        },
        {
          text: "Custom Car",
          details: "from TGRS category",
          highlight: "(< ₹18,00,000)",
        },
        {
          text: "Custom Bike",
          details: "from ANY category",
          highlight: "(< ₹1,00,000)",
        },
        {
          text: "Custom Numberplate",
          details: "",
          highlight: "(4 alphabets 4 numbers)",
          example: "TGRS1234",
        },
        {
          text: "In-game Cash",
          details: "₹30,000",
        },
        {
          text: "Lucky Wheel Coins",
          details: "750",
        },
      ],
      timeFeatures: [
        { text: "VIP Role", duration: "1 month" },
        { text: "Priority Queue", duration: "1 month" },
        { text: "Rental Apartment", duration: "1 month" },
      ],
      clothing: [
        {
          name: "TGG Pant",
          variants: 4,
          description: "Exclusive pants with TGRS branding and premium materials",
          placeholder: "[Add VIP 2 Clothing Name Here]",
        },
      ],
      peds: [],
      pets: [],
      tiltDirection: "right" as const,
      pattern: "hexagon" as const,
    },
    {
      tier: 3,
      price: 999,
      title: "VIP 3",
      description: "Premium experience with exclusive content",
      icon: "fas fa-trophy",
      isPopular: false,
      mainFeatures: [
        {
          text: "Custom Phone Number",
          details: "with 6 digits",
          highlight: "(5 number sequence max)",
          example: "444441",
        },
        {
          text: "Custom Car",
          details: "from TGRS category",
          highlight: "(< ₹25,00,000)",
        },
        {
          text: "Custom Bike",
          details: "from ANY category",
          highlight: "(< ₹2,50,000)",
        },
        {
          text: "Custom Numberplate x2",
          details: "",
          highlight: "(3 alphabets 3 numbers)",
          example: "TGR123",
        },
        {
          text: "In-game Cash",
          details: "₹50,000",
        },
        {
          text: "Lucky Wheel Coins",
          details: "1250",
        },
        {
          text: "Tax Free",
          details: "No Taxes",
        },
        {
          text: "Extra Character Slot",
          details: "+1 slot",
        },
      ],
      timeFeatures: [
        { text: "VIP Role", duration: "1 month" },
        { text: "Priority Queue", duration: "1 month" },
        { text: "Tier 1 House", duration: "1 month" },
      ],
      clothing: [
        {
          name: "TGYeezes",
          variants: 5,
          description: "Premium TGRS edition footwear with exclusive design patterns",
          placeholder: "[Add VIP 3 Clothing Name Here]",
        },
      ],
      peds: [
        { name: "Brenda", image: "/assets/peds/Brenda.jpg" },
        { name: "Kim Ji Won", image: "/assets/peds/Kim Ji Won.png" },
        { name: "Rhodeey", image: "/assets/peds/Rhodeey.jpg" },
        { name: "Leo Fric", image: "/assets/peds/Leo Fric.jpg" },
      ],
      pets: [],
      tiltDirection: "left" as const,
      pattern: "dots" as const,
    },
    {
      tier: 4,
      price: 1699,
      title: "VIP 4",
      description: "Elite tier with premium vehicles and housing",
      icon: "fas fa-gem",
      mainFeatures: [
        {
          text: "Custom Phone Number",
          details: "with 5 digits",
          highlight: "(4 number sequence max)",
          example: "44441",
        },
        {
          text: "Custom Car",
          details: "from TGRS category",
          highlight: "(< ₹25,00,000 & < ₹10,00,000)",
        },
        {
          text: "Custom Bike",
          details: "from TGRS category",
          highlight: "(< ₹3,50,000)",
        },
        {
          text: "Custom Numberplate x3",
          details: "",
          highlight: "(2 alphabets 2 numbers)",
          example: "TG12",
        },
        {
          text: "Boat",
          details: "Standard model",
        },
        {
          text: "In-game Cash",
          details: "₹75,000",
        },
        {
          text: "Lucky Wheel Coins",
          details: "1500",
        },
        {
          text: "Tax Free",
          details: "No Taxes",
        },
        {
          text: "Extra Character Slot",
          details: "+1 slot",
        },
        {
          text: "Mechanic Discount",
          details: "5% off repairs",
        },
        {
          text: "Permanent Apartment",
          details: "Forever ownership",
        },
      ],
      timeFeatures: [
        { text: "VIP Role", duration: "1 month" },
        { text: "Priority Queue Premium", duration: "1 month" },
        { text: "Tier 2 House", duration: "1 month" },
      ],
      clothing: [
        {
          name: "TGYeezes",
          variants: 5,
          description: "Premium TGRS edition footwear with luxury materials and VIP branding",
          placeholder: "[Add VIP 4 Clothing Name Here]",
        },
      ],
      peds: [
        { name: "Selena", image: "/assets/peds/Selena.jpg" },
        { name: "Jimmy", image: "/assets/peds/Jimmy.jpg" },
        { name: "Will Stone", image: "/assets/peds/Will Stone.jpg" },
        { name: "Manuel", image: "/assets/peds/Manuel.jpg" },
        { name: "Spencer", image: "/assets/peds/Spencer.jpg" },
        { name: "Carter80", image: "/assets/peds/Carter80.png" },
        { name: "P Ballas", image: "/assets/peds/P Ballas.png" },
        { name: "Vicente", image: "/assets/peds/Vicente.jpg" },
        { name: "Milton", image: "/assets/peds/Milton.jpg" },
      ],
      pets: [],
      tiltDirection: "right" as const,
      pattern: "grid" as const,
    },
    {
      tier: 5,
      price: 2899,
      title: "VIP 5",
      description: "Ultimate VIP experience with everything unlocked",
      icon: "fas fa-crown",
      mainFeatures: [
        {
          text: "Custom Phone Number",
          details: "with 3 digits",
          highlight: "(2 number sequence max)",
          example: "101-999",
        },
        {
          text: "Custom Car",
          details: "from TGRS category",
          highlight: "(< ₹45,00,000, ₹18,00,000)",
        },
        {
          text: "Custom Bike",
          details: "from TGRS category",
          highlight: "(< ₹5,00,000)",
        },
        {
          text: "Helicopter",
          details: "1 premium model",
          highlight: "(One time)",
        },
        {
          text: "Premium Boat",
          details: "1 luxury model",
          highlight: "(One time)",
        },
        {
          text: "Custom Numberplate x4",
          details: "",
          highlight: "(2 alphabets 2 numbers)",
          example: "TG12",
        },
        {
          text: "In-game Cash",
          details: "₹100,000",
        },
        {
          text: "Lucky Wheel Coins",
          details: "2000",
        },
        {
          text: "Tax Free",
          details: "No Taxes",
        },
        {
          text: "Extra Character Slot",
          details: "+1 slot",
        },
        {
          text: "Mechanic Discount",
          details: "10% off repairs",
        },
        {
          text: "Tier 1 House",
          details: "Permanent ownership",
        },
      ],
      timeFeatures: [
        { text: "VIP Role", duration: "1 month" },
        { text: "Priority Queue", duration: "1 month" },
        { text: "Tier 5 House with Helipad", duration: "1 month" },
      ],
      clothing: [
        {
          name: "Full Clothing Set",
          variants: 0,
          description: "Complete collection from VIP1 to VIP5 with exclusive ultimate tier designs",
          placeholder: "[Add VIP 5 Ultimate Clothing Set Name Here]",
        },
        {
          name: "TGYeezes",
          variants: 5,
          description: "Premium TGRS edition footwear with diamond-tier exclusive styling",
          placeholder: "[Add VIP 5 Premium Footwear Name Here]",
        },
      ],
      peds: [
        { name: "Adeline", image: "/assets/peds/Adeline.jpg" },
        { name: "Akira", image: "/assets/peds/Akira.jpg" },
        { name: "Ana Jasmine", image: "/assets/peds/Ana Jasmine.jpg" },
        { name: "Andreas", image: "/assets/peds/Andreas.png" },
        { name: "Anita", image: "/assets/peds/Anita.png" },
        { name: "Antonio", image: "/assets/peds/Antonio.png" },
        { name: "Beany", image: "/assets/peds/Beany.jpg" },
        { name: "Bjorn", image: "/assets/peds/Bjorn.jpg" },
        { name: "Bobby", image: "/assets/peds/Bobby.jpg" },
        { name: "Brucie Kibbutz", image: "/assets/peds/Brucie Kibbutz.png" },
        { name: "Carlos", image: "/assets/peds/Carlos.jpg" },
        { name: "Catline", image: "/assets/peds/Catline.jpg" },
        { name: "Cheng", image: "/assets/peds/Cheng.jpg" },
        { name: "Dario Mafia", image: "/assets/peds/Dario Mafia.jpg" },
        { name: "Diyo80", image: "/assets/peds/Diyo80.jpg" },
        { name: "Djarot", image: "/assets/peds/Djarot.jpg" },
        { name: "Estina Meil", image: "/assets/peds/Estina Meil.jpg" },
        { name: "Ethan", image: "/assets/peds/Ethan.jpg" },
        { name: "Geo", image: "/assets/peds/Geo.jpg" },
        { name: "Gregor", image: "/assets/peds/Gregor.jpg" },
        { name: "Hao", image: "/assets/peds/Hao.png" },
        { name: "Hendrix", image: "/assets/peds/Hendrix.jpg" },
        { name: "Hendry80", image: "/assets/peds/Hendry80.png" },
        { name: "Jacob", image: "/assets/peds/Jacob.jpg" },
        { name: "Jennifer", image: "/assets/peds/Jennifer.jpg" },
        { name: "Jimenez", image: "/assets/peds/Jimenez.png" },
        { name: "Jonson", image: "/assets/peds/Jonson.jpg" },
        { name: "Julio Bert", image: "/assets/peds/Julio Bert.jpg" },
        { name: "Lucia", image: "/assets/peds/Lucia.jpg" },
        { name: "Marcella", image: "/assets/peds/Marcella.jpg" },
        { name: "Marlo", image: "/assets/peds/Marlo.jpg" },
        { name: "Martinez", image: "/assets/peds/Martinez.jpg" },
        { name: "Michelle", image: "/assets/peds/Michelle.jpg" },
        { name: "Moretio", image: "/assets/peds/Moretio.jpg" },
        { name: "Norman", image: "/assets/peds/Norman.jpg" },
        { name: "Pablo", image: "/assets/peds/Pablo.jpg" },
        { name: "Pietro", image: "/assets/peds/Pietro.jpg" },
        { name: "Raymond", image: "/assets/peds/Raymond.jpg" },
        { name: "Rocky", image: "/assets/peds/Rocky.png" },
        { name: "Roscoe", image: "/assets/peds/Roscoe.png" },
        { name: "Velden", image: "/assets/peds/Velden.jpg" },
        { name: "Yuna", image: "/assets/peds/Yuna.jpg" },
      ],
      pets: [],
      isPopular: true,
      tiltDirection: "left" as const,
      pattern: "circuit" as const,
    },
  ];

  return (
    <>
      <Navbar />

      {/* Hero Section */}
      <Hero
        title="VIP PACKAGES"
        subtitle="Unlock exclusive features and enhance your roleplay experience with our premium VIP packages"
        primaryButtonText="Contact Admin"
        primaryButtonHref="https://discord.gg/GAMravHDnB"
        secondaryButtonText="View Features"
        secondaryButtonHref="#packages"
        backgroundImage="/assets/vip.jpg"
      />

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* VIP Benefits Overview */}
      <section className="py-12 md:py-20 relative bg-black/50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg">
              Why Choose <span className="text-neon-orange">VIP</span>?
            </h2>
            <p className="text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2">
              Our VIP packages are designed to enhance your roleplay experience with exclusive features,
              priority support, and unique customization options that set you apart in the TGRS community.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 mb-12 md:mb-16">
            <div className="text-center group p-4">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform">
                <i className="fas fa-star text-neon-orange text-lg sm:text-2xl"></i>
              </div>
              <h3 className="text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300">Exclusive Features</h3>
              <p className="text-gray-400 text-xs sm:text-sm">Access unique customization options and premium content</p>
            </div>

            <div className="text-center group p-4">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform">
                <i className="fas fa-headset text-neon-orange text-lg sm:text-2xl"></i>
              </div>
              <h3 className="text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300">Priority Support</h3>
              <p className="text-gray-400 text-xs sm:text-sm">Get faster response times and dedicated assistance</p>
            </div>

            <div className="text-center group p-4">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform">
                <i className="fas fa-users text-neon-orange text-lg sm:text-2xl"></i>
              </div>
              <h3 className="text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300">VIP Community</h3>
              <p className="text-gray-400 text-xs sm:text-sm">Join an exclusive community of premium members</p>
            </div>

            <div className="text-center group p-4">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-3 sm:mb-4 group-hover:bg-neon-orange/30 transition-colors duration-300 group-hover:scale-110 transform">
                <i className="fas fa-gem text-neon-orange text-lg sm:text-2xl"></i>
              </div>
              <h3 className="text-white font-semibold mb-2 text-sm sm:text-base group-hover:text-neon-orange transition-colors duration-300">Great Value</h3>
              <p className="text-gray-400 text-xs sm:text-sm">One-time purchase with lifetime benefits</p>
            </div>
          </div>

          {/* VIP Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 md:gap-8">
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">500+</div>
              <div className="text-gray-400 text-xs sm:text-sm">VIP Members</div>
            </div>
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">50+</div>
              <div className="text-gray-400 text-xs sm:text-sm">Exclusive Peds</div>
            </div>
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">100+</div>
              <div className="text-gray-400 text-xs sm:text-sm">Custom Vehicles</div>
            </div>
            <div className="text-center p-3 sm:p-4">
              <div className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-neon-orange mb-2">24/7</div>
              <div className="text-gray-400 text-xs sm:text-sm">VIP Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* VIP Packages Section */}
      <section id="packages" className="py-12 md:py-20 relative bg-black">
        <CashFlyingAnimation />
        <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 relative z-10">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg">
              Choose Your <span className="text-neon-orange">VIP Tier</span>
            </h2>
            <p className="text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2">
              Select the perfect VIP package that suits your needs and budget. All packages include lifetime access to their respective features.
            </p>
          </div>

          {/* VIP Cards - Mobile: 1 per row, Tablet: 2 per row, Desktop: 3 on top, 2 on bottom */}
          <div className="space-y-8 sm:space-y-12 max-w-7xl mx-auto">
            {/* Top Row - VIP 1, 2, 3 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 items-stretch">
              {vipTiers.slice(0, 3).map((tier) => (
                <VIPCard
                  key={tier.tier}
                  tier={tier.tier}
                  price={tier.price}
                  title={tier.title}
                  description={tier.description}
                  icon={tier.icon}
                  mainFeatures={tier.mainFeatures}
                  timeFeatures={tier.timeFeatures}
                  clothing={tier.clothing}
                  peds={tier.peds}
                  isPopular={tier.isPopular}
                  tiltDirection={tier.tiltDirection}
                  pattern={tier.pattern}
                  className="animate-slide-in-left"
                />
              ))}
            </div>

            {/* Bottom Row - VIP 4, 5 (centered) */}
            <div className="flex justify-center">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 sm:gap-8 w-full max-w-5xl items-stretch">
                {vipTiers.slice(3, 5).map((tier) => (
                  <VIPCard
                    key={tier.tier}
                    tier={tier.tier}
                    price={tier.price}
                    title={tier.title}
                    description={tier.description}
                    icon={tier.icon}
                    mainFeatures={tier.mainFeatures}
                    timeFeatures={tier.timeFeatures}
                    clothing={tier.clothing}
                    peds={tier.peds}
                    isPopular={tier.isPopular}
                    tiltDirection={tier.tiltDirection}
                    pattern={tier.pattern}
                    className="animate-slide-in-left"
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* VIP Rules & Guidelines Section */}
      <section className="py-12 md:py-20 relative bg-black">
        <div className="absolute inset-0 pattern-hexagon opacity-5"></div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-display font-bold text-white mb-4 wave-heading-bg">
              <i className="fas fa-gavel text-neon-orange mr-2 sm:mr-4 text-lg sm:text-xl md:text-2xl"></i>
              VIP Rules & <span className="text-neon-orange">Guidelines</span>
            </h2>
            <p className="text-sm sm:text-base text-gray-400 max-w-3xl mx-auto px-2">
              Important information and guidelines for all VIP members. Please read carefully to ensure you understand all terms and conditions.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
            {/* Rule 1 */}
            <div className="flex items-start space-x-3 sm:space-x-6 p-4 sm:p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group">
              <div className="flex-shrink-0">
                <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors">
                  <i className="fas fa-clock text-neon-orange text-sm sm:text-lg"></i>
                </div>
              </div>
              <div className="flex-grow">
                <h3 className="text-white font-bold text-base sm:text-lg mb-2">VIP Duration Policy</h3>
                <p className="text-gray-300 leading-relaxed text-sm sm:text-base">
                  VIP benefits are active for <span className="text-neon-orange font-semibold">1 month from the date of purchase</span> unless specified otherwise in your tier package.
                </p>
              </div>
            </div>

            {/* Rule 2 */}
            <div className="flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors">
                  <i className="fas fa-car text-neon-orange text-lg"></i>
                </div>
              </div>
              <div className="flex-grow">
                <h3 className="text-white font-bold text-lg mb-2">Vehicle Selection Rules</h3>
                <p className="text-gray-300 leading-relaxed">
                  Custom vehicles must be selected from the <span className="text-neon-orange font-semibold">TGRS category</span> within the specified price range for your VIP tier.
                </p>
              </div>
            </div>

            {/* Rule 3 */}
            <div className="flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors">
                  <i className="fas fa-mobile-alt text-neon-orange text-lg"></i>
                </div>
              </div>
              <div className="flex-grow">
                <h3 className="text-white font-bold text-lg mb-2">Custom Numbers & Plates</h3>
                <p className="text-gray-300 leading-relaxed">
                  Custom phone numbers and numberplates are <span className="text-neon-orange font-semibold">subject to availability</span> and must follow server guidelines for appropriate content.
                </p>
              </div>
            </div>

            {/* Rule 4 */}
            <div className="flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-red-500 hover:bg-gray-900/60 transition-all duration-300 group">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-red-500/20 flex items-center justify-center group-hover:bg-red-500/30 transition-colors">
                  <i className="fas fa-exclamation-triangle text-red-400 text-lg"></i>
                </div>
              </div>
              <div className="flex-grow">
                <h3 className="text-white font-bold text-lg mb-2">Phone Number Restrictions</h3>
                <p className="text-gray-300 leading-relaxed">
                  Custom phone numbers cannot use <span className="text-red-400 font-semibold">emergency numbers (e.g., 911)</span> and are limited to the sequence length specified in your VIP tier.
                </p>
              </div>
            </div>

            {/* Rule 5 */}
            <div className="flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors">
                  <i className="fas fa-home text-neon-orange text-lg"></i>
                </div>
              </div>
              <div className="flex-grow">
                <h3 className="text-white font-bold text-lg mb-2">Housing Benefits</h3>
                <p className="text-gray-300 leading-relaxed">
                  Some housing benefits expire after <span className="text-neon-orange font-semibold">1 month</span> and do not include furniture or decorations unless specifically mentioned.
                </p>
              </div>
            </div>

            {/* Rule 6 */}
            <div className="flex items-start space-x-6 p-6 rounded-xl bg-gray-900/40 border-l-4 border-neon-orange hover:bg-gray-900/60 transition-all duration-300 group">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 rounded-full bg-neon-orange/20 flex items-center justify-center group-hover:bg-neon-orange/30 transition-colors">
                  <i className="fas fa-shield-alt text-neon-orange text-lg"></i>
                </div>
              </div>
              <div className="flex-grow">
                <h3 className="text-white font-bold text-lg mb-2">Server Rules Compliance</h3>
                <p className="text-gray-300 leading-relaxed">
                  All VIP members must adhere to server rules. <span className="text-neon-orange font-semibold">VIP status does not exempt players</span> from following community guidelines.
                </p>
              </div>
            </div>
          </div>

          {/* Simple Notice */}
          <div className="mt-12 max-w-4xl mx-auto text-center">
            <div className="glass rounded-xl p-6 border border-neon-orange/30">
              <h3 className="text-white font-bold text-xl mb-4">Important Notice</h3>
              <p className="text-gray-300 leading-relaxed mb-6">
                By purchasing VIP, you agree to follow these rules and guidelines. The staff team reserves the right to modify these terms at any time.
              </p>
              <a
                href="https://discord.gg/GAMravHDnB"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-neon-orange to-neon-red text-black font-semibold rounded-lg hover:shadow-neon-strong transition-all duration-300"
              >
                <i className="fab fa-discord mr-2"></i>
                Join Discord for Support
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Section Divider */}
      <div className="section-divider"></div>

      {/* Contact Section */}
      <section className="py-8 sm:py-12 md:py-16 relative bg-black/50">
        <div className="container mx-auto px-4 md:px-6">
          {/* Contact Information */}
          <div className="text-center">
            <div className="glass rounded-lg p-4 sm:p-6 md:p-8 border border-neon-orange/30 max-w-4xl mx-auto">
              <h3 className="text-lg sm:text-xl md:text-2xl font-display font-bold text-white mb-3 sm:mb-4">
                Ready to Upgrade Your Experience?
              </h3>
              <p className="text-xs sm:text-sm md:text-base text-gray-400 mb-4 sm:mb-6 px-2">
                Contact our admin team on Discord to purchase your VIP package.
                Payment methods and additional details will be provided upon contact.
              </p>

              {/* Quick Benefits Summary */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
                <div className="text-center p-2 sm:p-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3">
                    <i className="fas fa-bolt text-neon-orange text-xs sm:text-sm md:text-base"></i>
                  </div>
                  <h4 className="text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base">Instant Activation</h4>
                  <p className="text-gray-400 text-xs sm:text-sm">VIP features activated immediately after purchase</p>
                </div>
                <div className="text-center p-2 sm:p-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3">
                    <i className="fas fa-shield-alt text-neon-orange text-xs sm:text-sm md:text-base"></i>
                  </div>
                  <h4 className="text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base">Secure Payment</h4>
                  <p className="text-gray-400 text-xs sm:text-sm">Safe and secure payment processing</p>
                </div>
                <div className="text-center p-2 sm:p-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full bg-neon-orange/20 flex items-center justify-center mx-auto mb-2 sm:mb-3">
                    <i className="fas fa-infinity text-neon-orange text-xs sm:text-sm md:text-base"></i>
                  </div>
                  <h4 className="text-white font-semibold mb-1 sm:mb-2 text-xs sm:text-sm md:text-base">Lifetime Access</h4>
                  <p className="text-gray-400 text-xs sm:text-sm">One-time purchase, lifetime benefits</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </>
  );
}
